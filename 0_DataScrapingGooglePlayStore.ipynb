{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {"metadata": {}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Python implementation: CPython\n", "Python version       : 3.12.3\n", "IPython version      : 8.23.0\n", "\n", "pandas             : 2.2.2\n", "matplotlib         : 3.9.0\n", "seaborn            : 0.13.2\n", "google_play_scraper: 1.2.6\n", "\n"]}], "source": ["%reload_ext watermark\n", "%watermark -v -p pandas,mat<PERSON><PERSON><PERSON>b,seaborn,google_play_scraper"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"metadata": {}}, "outputs": [], "source": ["#!pip install google_play_scraper"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"metadata": {}}, "outputs": [], "source": ["import json\n", "import pandas as pd\n", "from tqdm import tqdm\n", "\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "from pygments import highlight\n", "from pygments.lexers import JsonLexer\n", "from pygments.formatters import TerminalFormatter\n", "\n", "from google_play_scraper import Sort, reviews, app\n", "\n", "%matplotlib inline\n", "%config InlineBackend.figure_format='retina'\n", "\n", "sns.set(style='whitegrid', palette='muted', font_scale=1.2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Bank Terbaik 2025\n", "\n", "https://goodstats.id/article/pilihan-bank-digital-terbaik-di-indonesia-dengan-bebas-biaya-admin-l5XCS\n", "\n", "<PERSON><PERSON><PERSON>, bank digital merupakan layanan perbankan berbasis internet yang memungkinkan nasabah dapat menikmati seluruh fasilitas tanpa harus datang ke kantor cabang. Adanya kemudahan ini bisa menjadi nilai tambah, terlebih layanan tersebut bebas dari biaya administrasi bulanan. \n", "\n", "Berikut ini deretan pilihan bank digital terbaik di Indonesia yang menawarkan layanan bebas admin dan berbagai fitur menarik lainnya. \n", "\n", "1. Blu BCA\n", "2. Sea Bank\n", "3. <PERSON><PERSON> BTPN\n", "4. Bank Jago\n", "5. Neo Bank\n", "\n", "| Bank Digital | Fitur Unggulan |\n", "|--------------|----------------|\n", "| Blu by BCA | Blusaving, BluGether, BluDeposit, BluPay, BluRewards (cashback) |\n", "| SeaBank\t| Bunga 3,5%/tahun, bebas transfer, promo belanja <PERSON> |\n", "| Jenius\t| Budgeting tools, e-card, gratis transfer antar bank |\n", "| Bank Jago | 60 kantong tabungan, 150x transfer gratis/bulan, investasi mudah |\n", "| Neo Bank | Bunga hingga 8%/tahun, k<PERSON> diskon, layanan 24jam |\n", "\n", "\n", "\n", "\n", "https://bprartomoro.co.id/8-daftar-bank-digital-indonesia-yang-cocok-untuk-anak-muda/\n", "1. Bank Jago\n", "2. Blu BCA\n", "3. Sea Bank\n", "4. Neo Bank\n", "5. <PERSON><PERSON> BTPN\n", "6. Allo Bank\n", "7. TMRW UOB\n", "8. Digibank DBS\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Bank Terbaik 2024\n", "### Bank Digital dan <PERSON> Pasarnya (Februari 2023)\n", "https://databoks.katadata.co.id/teknologi-telekomunikasi/statistik/4b5316c0904a77f/deretan-bank-digital-dan-nilai-kapitalisasi-pasarnya-siapa-paling-besar\n", "\n", "\n", "1. <PERSON> (ARTO) Rp39,91 triliun\n", "2. Allo Bank (BBHI) Rp36,07 triliun\n", "3. <PERSON> (BANK) Rp19,27 triliun\n", "4. <PERSON> (AGRO) Rp10,59 triliun\n", "5. Bank Neo Commerce (BBYB) Rp8,07 triliun\n", "   \n", "### <PERSON><PERSON><PERSON><PERSON>\n", "https://kumparan.com/berita-bisnis/5-bank-digital-terbaik-di-indonesia-untuk-bertransaksi-23USjxqLAEA/full\n", "\n", "1. Blu BCA\n", "2. SeaBank\n", "3. <PERSON><PERSON>\n", "4. Bank Jago\n", "5. Neo Bank\n", "\n", "### <PERSON><PERSON><PERSON><PERSON> Katadata Q1 2024\n", "https://katadata.co.id/finansial/keuangan/6630cd0d0a239/adu-kuat-laba-tiga-bank-digital-di-q1-2024-siapa-jawaranya\n", "1. Bank Jago\n", "2. Bank Raya\n", "3. Allo Bank\n", "\n", "### <PERSON><PERSON><PERSON><PERSON> TOPSIS Unesa (Jurnal)\n", "https://ejournal.unesa.ac.id/index.php/mathunesa/article/view/53084\n", "\n", "1. <PERSON> Jago (B1) 0,9407  \n", "2. SeaBank (B4) 0,7998  \n", "3. <PERSON><PERSON> (B3) 0,3741  \n", "4. <PERSON> (B5) 0,2482  \n", "5. <PERSON><PERSON><PERSON><PERSON> (B2) 0,0051\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"metadata": {}}, "outputs": [], "source": ["app_packages = [\n", "  'com.alloapp.yump', #allo bank\n", "  'com.jago.digitalBanking', #bank jago\n", "  # 'com.senyumkubank.rekeningonline', #amarbank\n", "  # 'id.aladinbank.mobile',\n", "  'id.co.bankbkemobile.digitalbank', #seabank\n", "  'com.btpn.dc', #btpn jenius\n", "  'com.bcadigital.blu', #bank bca\n", "  'id.co.bankraya.apps', #rayabank\n", "  'com.bnc.finance' #neobank\n", "#   'com.supercell.brawlstars',\n", "#   'jp.pokemon.pokemonunite',\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"metadata": {}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 7/7 [00:22<00:00,  3.17s/it]\n"]}], "source": ["app_infos = []\n", "\n", "for ap in tqdm(app_packages):\n", "  info = app(ap, lang='id', country='id')\n", " # del info['comments']\n", "  app_infos.append(info)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df=pd.DataFrame(app_infos)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"metadata": {}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>title</th>\n", "      <th>description</th>\n", "      <th>descriptionHTML</th>\n", "      <th>summary</th>\n", "      <th>installs</th>\n", "      <th>min<PERSON>nstalls</th>\n", "      <th>realInstalls</th>\n", "      <th>score</th>\n", "      <th>ratings</th>\n", "      <th>reviews</th>\n", "      <th>...</th>\n", "      <th>contentRatingDescription</th>\n", "      <th>adSupported</th>\n", "      <th>containsAds</th>\n", "      <th>released</th>\n", "      <th>lastUpdatedOn</th>\n", "      <th>updated</th>\n", "      <th>version</th>\n", "      <th>comments</th>\n", "      <th>appId</th>\n", "      <th>url</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Allo Bank</td>\n", "      <td>Hi, kini Allo Bank untuk Android hadir!\\r\\n\\r\\...</td>\n", "      <td>Hi, kini Allo Bank untuk Android hadir!&lt;br&gt;&lt;br...</td>\n", "      <td>Allo Bank, perbankan digital baru yang akan me...</td>\n", "      <td>5.000.000+</td>\n", "      <td>5000000</td>\n", "      <td>8161673</td>\n", "      <td>4.119869</td>\n", "      <td>42666</td>\n", "      <td>26596</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>14 Mar 2022</td>\n", "      <td>20 Sep 2024</td>\n", "      <td>**********</td>\n", "      <td>1.44.08</td>\n", "      <td>[]</td>\n", "      <td>com.alloapp.yump</td>\n", "      <td>https://play.google.com/store/apps/details?id=...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Jago/Jago <PERSON> bank digital</td>\n", "      <td>Jago/<PERSON><PERSON> digital bank adalah aplikasi...</td>\n", "      <td>Jago/<PERSON><PERSON> digital bank adalah aplikasi...</td>\n", "      <td><PERSON><PERSON><PERSON>, transaksi, bayar QRIS &amp; atur uang sema...</td>\n", "      <td>10.000.000+</td>\n", "      <td>********</td>\n", "      <td>********</td>\n", "      <td>4.542278</td>\n", "      <td>169334</td>\n", "      <td>55927</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>13 Apr 2021</td>\n", "      <td>13 Sep 2024</td>\n", "      <td>**********</td>\n", "      <td>8.50.0</td>\n", "      <td>[]</td>\n", "      <td>com.jago.digitalBanking</td>\n", "      <td>https://play.google.com/store/apps/details?id=...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SeaBank</td>\n", "      <td><PERSON><PERSON><PERSON> Untung di SeaBank!\\r\\nBuka rekening SeaB...</td>\n", "      <td>Lebih Untung di SeaBank!&lt;br&gt;B<PERSON> rekening SeaB...</td>\n", "      <td>Aplikasi perbankan digital untuk lakukan aktiv...</td>\n", "      <td>10.000.000+</td>\n", "      <td>********</td>\n", "      <td>********</td>\n", "      <td>4.872240</td>\n", "      <td>1212285</td>\n", "      <td>167517</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>26 Feb 2021</td>\n", "      <td>19 Sep 2024</td>\n", "      <td>**********</td>\n", "      <td>3.3.4</td>\n", "      <td>[]</td>\n", "      <td>id.co.bankbkemobile.digitalbank</td>\n", "      <td>https://play.google.com/store/apps/details?id=...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON> mudah atur kehidupan/keuangan dengan apli...</td>\n", "      <td><PERSON> mudah atur kehidupan/keuangan dengan apli...</td>\n", "      <td><PERSON> mudah atur kehidupan/keuangan dengan apli...</td>\n", "      <td>10.000.000+</td>\n", "      <td>********</td>\n", "      <td>********</td>\n", "      <td>3.464688</td>\n", "      <td>201451</td>\n", "      <td>128641</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>10 Agu 2016</td>\n", "      <td>18 Sep 2024</td>\n", "      <td>**********</td>\n", "      <td>4.8.1</td>\n", "      <td>[]</td>\n", "      <td>com.btpn.dc</td>\n", "      <td>https://play.google.com/store/apps/details?id=...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>blu by BCA Digital</td>\n", "      <td>blu Siap Jadi Pegangan Finansialmu!\\r\\n \\r\\nbl...</td>\n", "      <td>blu Siap Jadi Pegangan Finansialmu!&lt;br&gt; &lt;br&gt;bl...</td>\n", "      <td><PERSON><PERSON>, Men<PERSON>ung, Buka ...</td>\n", "      <td>1.000.000+</td>\n", "      <td>1000000</td>\n", "      <td>3411494</td>\n", "      <td>4.625711</td>\n", "      <td>93852</td>\n", "      <td>26181</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>17 Jun 2021</td>\n", "      <td>7 Sep 2024</td>\n", "      <td>**********</td>\n", "      <td>1.55.0</td>\n", "      <td>[]</td>\n", "      <td>com.bcadigital.blu</td>\n", "      <td>https://play.google.com/store/apps/details?id=...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Raya - Digital Bank</td>\n", "      <td>Bank digital pilihan untuk kelola keuangan per...</td>\n", "      <td>Bank digital pilihan untuk kelola keuangan per...</td>\n", "      <td><PERSON><PERSON><PERSON> aman, mudah dan menyenangkan #Rayakanlah</td>\n", "      <td>1.000.000+</td>\n", "      <td>1000000</td>\n", "      <td>1157997</td>\n", "      <td>4.518599</td>\n", "      <td>8796</td>\n", "      <td>6803</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>31 Jan 2022</td>\n", "      <td>30 Agu 2024</td>\n", "      <td>**********</td>\n", "      <td>2.19.0</td>\n", "      <td>[]</td>\n", "      <td>id.co.bankraya.apps</td>\n", "      <td>https://play.google.com/store/apps/details?id=...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>neobank dari BNC Digital</td>\n", "      <td>\"Saat<PERSON> nikmati berbagai kemudahan buat semua...</td>\n", "      <td>&amp;quot;Saatnya nikmati berbagai kemudahan buat ...</td>\n", "      <td>Layanan digital dari Bank Neo Commerce: tabung...</td>\n", "      <td>10.000.000+</td>\n", "      <td>********</td>\n", "      <td>********</td>\n", "      <td>3.555225</td>\n", "      <td>273846</td>\n", "      <td>154418</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>22 Mar 2021</td>\n", "      <td>19 Sep 2024</td>\n", "      <td>**********</td>\n", "      <td>3.2.81</td>\n", "      <td>[]</td>\n", "      <td>com.bnc.finance</td>\n", "      <td>https://play.google.com/store/apps/details?id=...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7 rows × 45 columns</p>\n", "</div>"], "text/plain": ["                            title  \\\n", "0                       Allo Bank   \n", "1  Jago/Jago Syariah bank digital   \n", "2                         SeaBank   \n", "3                          Jen<PERSON>   \n", "4              blu by BCA Digital   \n", "5             Raya - Digital Bank   \n", "6        neobank dari BNC Digital   \n", "\n", "                                         description  \\\n", "0  Hi, kini Allo Bank untuk Android hadir!\\r\\n\\r\\...   \n", "1  Jago/Jago Syariah digital bank adalah aplikasi...   \n", "2  Lebih Untung di SeaBank!\\r\\nBuka rekening SeaB...   \n", "3  Cara mudah atur kehidupan/keuangan dengan apli...   \n", "4  blu Siap Jadi Pegangan Finansialmu!\\r\\n \\r\\nbl...   \n", "5  Bank digital pilihan untuk kelola keuangan per...   \n", "6  \"Saat<PERSON> nikmati berbagai kemudahan buat semua...   \n", "\n", "                                     descriptionHTML  \\\n", "0  Hi, kini Allo Bank untuk Android hadir!<br><br...   \n", "1  Jago/Jago Syariah digital bank adalah aplikasi...   \n", "2  Lebih Untung di SeaBank!<br><PERSON><PERSON> rekening SeaB...   \n", "3  Cara mudah atur kehidupan/keuangan dengan apli...   \n", "4  blu Siap Jadi Pegangan Finansialmu!<br> <br>bl...   \n", "5  Bank digital pilihan untuk kelola keuangan per...   \n", "6  &quot;<PERSON><PERSON><PERSON> nikmati berbagai kemudahan buat ...   \n", "\n", "                                             summary     installs  \\\n", "0  Allo Bank, perbankan digital baru yang akan me...   5.000.000+   \n", "1  Nabung, transaksi, bayar QRIS & atur uang sema...  10.000.000+   \n", "2  Aplikasi perbankan digital untuk lakukan aktiv...  10.000.000+   \n", "3  Cara mudah atur kehidupan/keuangan dengan apli...  10.000.000+   \n", "4  <PERSON><PERSON>, <PERSON>abung, Buka ...   1.000.000+   \n", "5    <PERSON><PERSON><PERSON> aman, mudah dan menyenangkan #Rayakanlah   1.000.000+   \n", "6  Layanan digital dari Bank Neo Commerce: tabung...  10.000.000+   \n", "\n", "   minInstalls  realInstalls     score  ratings  reviews  ...  \\\n", "0      5000000       8161673  4.119869    42666    26596  ...   \n", "1     ********      ********  4.542278   169334    55927  ...   \n", "2     ********      ********  4.872240  1212285   167517  ...   \n", "3     ********      ********  3.464688   201451   128641  ...   \n", "4      1000000       3411494  4.625711    93852    26181  ...   \n", "5      1000000       1157997  4.518599     8796     6803  ...   \n", "6     ********      ********  3.555225   273846   154418  ...   \n", "\n", "  contentRatingDescription  adSupported  containsAds     released  \\\n", "0                     None        False        False  14 Mar 2022   \n", "1                     None        False        False  13 Apr 2021   \n", "2                     None        False        False  26 Feb 2021   \n", "3                     None        False        False  10 Agu 2016   \n", "4                     None        False        False  17 Jun 2021   \n", "5                     None        False        False  31 Jan 2022   \n", "6                     None        False        False  22 Mar 2021   \n", "\n", "   lastUpdatedOn     updated  version comments  \\\n", "0    20 Sep 2024  **********  1.44.08       []   \n", "1    13 Sep 2024  **********   8.50.0       []   \n", "2    19 Sep 2024  **********    3.3.4       []   \n", "3    18 Sep 2024  **********    4.8.1       []   \n", "4     7 Sep 2024  **********   1.55.0       []   \n", "5    30 Agu 2024  **********   2.19.0       []   \n", "6    19 Sep 2024  **********   3.2.81       []   \n", "\n", "                             appId  \\\n", "0                 com.alloapp.yump   \n", "1          com.jago.digitalBanking   \n", "2  id.co.bankbkemobile.digitalbank   \n", "3                      com.btpn.dc   \n", "4               com.bcadigital.blu   \n", "5              id.co.bankraya.apps   \n", "6                  com.bnc.finance   \n", "\n", "                                                 url  \n", "0  https://play.google.com/store/apps/details?id=...  \n", "1  https://play.google.com/store/apps/details?id=...  \n", "2  https://play.google.com/store/apps/details?id=...  \n", "3  https://play.google.com/store/apps/details?id=...  \n", "4  https://play.google.com/store/apps/details?id=...  \n", "5  https://play.google.com/store/apps/details?id=...  \n", "6  https://play.google.com/store/apps/details?id=...  \n", "\n", "[7 rows x 45 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"metadata": {}}, "outputs": [], "source": ["def print_json(json_object):\n", "  json_str = json.dumps(\n", "    json_object, \n", "    indent=2, \n", "    sort_keys=True, \n", "    default=str\n", "  )\n", "  print(highlight(json_str, JsonLexer(), TerminalFormatter()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"metadata": {}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"adSupported\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[34mfalse\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"appId\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"com.alloapp.yump\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"categories\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m[\u001b[37m\u001b[39;49;00m\n", "\u001b[37m    \u001b[39;49;00m{\u001b[37m\u001b[39;49;00m\n", "\u001b[37m      \u001b[39;49;00m\u001b[94m\"id\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"FINANCE\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m      \u001b[39;49;00m\u001b[94m\"name\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"Keuangan\"\u001b[39;49;00m\u001b[37m\u001b[39;49;00m\n", "\u001b[37m    \u001b[39;49;00m}\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m],\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"comments\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m[],\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"containsAds\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[34mfalse\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"contentRating\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"Rating 3+\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"contentRatingDescription\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[34mnull\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"currency\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"IDR\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"description\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"Hi, kini Allo Bank untuk Android hadir!\\r\\n\\r\\nAplikasi yang melengkapimu! Sebuah Bank Digital dan dompet digital dengan fitur Pay Later dan Instant Cash.\\r\\n\\r\\nPendaftaran yang mudah. Hanya dengan 5 menit untuk mendaftar secara online!\\r\\n\\r\\nBebas biaya admin! Tidak ada biaya admin bulanan, minimum setoran awal, minimum saldo serta gratis biaya transfer ke Bank lain menggunakan fitur BI-FAST.\\r\\n\\r\\nBunga yang menarik, sesuaikan kebutuhanmu dengan produk tabungan atau deposito digital!\\r\\n\\r\\nBuka deposito dengan cepat dan mudah. Real time melalui satu genggaman!\\r\\n\\r\\nTarik tunai tanpa kartu di ATM Bank Mega! Tarik uang tunai dengan mudah dan cepat untuk semua kebutuhan transaksimu.\\r\\n\\r\\nDan dapatkan banyak keuntungan di setiap transaksimu!\\r\\n\\r\\nInfo lebih lanjut\\r\\nAllo Care 080 4110 4110\\r\\nWhatsApp 0822 0822 4110\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"descriptionHTML\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"Hi, kini Allo Bank untuk Android hadir!<br><br>Aplikasi yang melengkapimu! Sebuah Bank Digital dan dompet digital dengan fitur Pay Later dan Instant Cash.<br><br>Pendaftaran yang mudah. Hanya dengan 5 menit untuk mendaftar secara online!<br><br>Bebas biaya admin! Tidak ada biaya admin bulanan, minimum setoran awal, minimum saldo serta gratis biaya transfer ke Bank lain menggunakan fitur BI-FAST.<br><br>Bung<PERSON> yang menarik, sesuaikan kebutuhanmu dengan produk tabungan atau deposito digital!<br><br>Buka deposito dengan cepat dan mudah. Real time melalui satu genggaman!<br><br>Tarik tunai tanpa kartu di ATM Bank Mega! Tarik uang tunai dengan mudah dan cepat untuk semua kebutuhan transaksimu.<br><br>Dan dapatkan banyak keuntungan di setiap transaksimu!<br><br>Info lebih lanjut<br>Allo Care 080 4110 4110<br>WhatsApp 0822 0822 4110\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"developer\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"PT. ALLO BANK INDONESIA Tbk\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"developerAddress\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"ASEAN Tower Building 3rd Floor, Jl K.H <PERSON>hudi No.10 Jakarta 10710, Indonesia\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"developerEmail\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"<EMAIL>\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"developerId\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"PT.+ALLO+BANK+INDONESIA+Tbk\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"developerWebsite\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"https://allobank.com\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"free\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[34mtrue\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"genre\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"Keuangan\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"genreId\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"FINANCE\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"headerImage\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"https://play-lh.googleusercontent.com/F-eWhDQuAoDNA-bcjqIMMEX_wkNzM6uz7Zn8BNfW3dHY-qojxn0ssVSLIr_W0FCwtzo\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"histogram\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m[\u001b[37m\u001b[39;49;00m\n", "\u001b[37m    \u001b[39;49;00m\u001b[34m7546\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m    \u001b[39;49;00m\u001b[34m1020\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m    \u001b[39;49;00m\u001b[34m1380\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m    \u001b[39;49;00m\u001b[34m1540\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m    \u001b[39;49;00m\u001b[34m31175\u001b[39;49;00m\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m],\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"icon\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"https://play-lh.googleusercontent.com/0gw4GVJoKuQCDIz8DOXt5fQDEy-RD0BDnQge-BsbnBaBTmXWgqjydABvetmCqTXE1Gm2\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"inAppProductPrice\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[34mnull\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"installs\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"5.000.000+\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"lastUpdatedOn\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"20 Sep 2024\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"minInstalls\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[34m5000000\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"offersIAP\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[34mfalse\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"originalPrice\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[34mnull\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"price\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[34m0\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"privacyPolicy\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"https://www.allobank.com/privacy\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"ratings\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[34m42666\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"realInstalls\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[34m8161673\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"released\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"14 Mar 2022\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"reviews\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[34m26596\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"sale\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[34mfalse\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"saleText\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[34mnull\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"saleTime\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[34mnull\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"score\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[34m4.1198688\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"screenshots\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m[\u001b[37m\u001b[39;49;00m\n", "\u001b[37m    \u001b[39;49;00m\u001b[33m\"https://play-lh.googleusercontent.com/XC05yvBoIc_Q15qYcqVfLwGuDw2k-nPVj4roZOLK9nVSbQl69x_XePdt-Q9YBe0-AGcs\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m    \u001b[39;49;00m\u001b[33m\"https://play-lh.googleusercontent.com/_BvUtqBYp6l2tSnfo9CYFzYXzIEVqHSSYSZxTfm8YZR8sm25t8RpdU_aXBfX7VVQKds\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m    \u001b[39;49;00m\u001b[33m\"https://play-lh.googleusercontent.com/UVu5eYJxSA3zJHiH6A7vWpNXdT5852fcIFhlDgCXjeGxYDzz7-c3YRtUMGY2UUYUrGuE\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m    \u001b[39;49;00m\u001b[33m\"https://play-lh.googleusercontent.com/wEgqZekLG3RLoxMaKSSPQWZi__-gjrenQ7ApJiWEz0DoEL4sgBXaCtB8MqS9pO89diQ\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m    \u001b[39;49;00m\u001b[33m\"https://play-lh.googleusercontent.com/2ZqKBa7_FlCt7GA-5cOnAG4HY54ReXsZemCeXKFkshIoaia_R6gZTXAzp-gxp0_z204\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m    \u001b[39;49;00m\u001b[33m\"https://play-lh.googleusercontent.com/Q40nApQHVxrLrKaV7qYRzMZN285WJ22Anbjljq2rDUwjuGHPWiGca-GJO66ZR_OJ0Es\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m    \u001b[39;49;00m\u001b[33m\"https://play-lh.googleusercontent.com/FwE1BARkc0Eyfa2WtohYSXY2fJ-EP0dyPAU5YIIhUsdQU5F5HZLpmCwcV7B0yVYr4Bi3\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m    \u001b[39;49;00m\u001b[33m\"https://play-lh.googleusercontent.com/qCBEsxSRs-s0nzuvk1__VZkxrupF5M-YxpfKUc0AJWw5qR0FpQeMlKm6KPt1xmGB2sI\"\u001b[39;49;00m\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m],\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"summary\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"Allo Bank, perbankan digital baru yang akan membantu Anda memenuhi semua kebutuhan <PERSON>.\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"title\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"Allo Bank\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"updated\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[34m**********\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"url\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"https://play.google.com/store/apps/details?id=com.alloapp.yump&hl=id&gl=id\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"version\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"1.44.08\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"video\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"https://www.youtube.com/embed/g-IhngxaCn8?ps=play&vq=large&rel=0&autohide=1&showinfo=0\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"videoImage\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"https://play-lh.googleusercontent.com/F-eWhDQuAoDNA-bcjqIMMEX_wkNzM6uz7Zn8BNfW3dHY-qojxn0ssVSLIr_W0FCwtzo\"\u001b[39;49;00m\u001b[37m\u001b[39;49;00m\n", "}\u001b[37m\u001b[39;49;00m\n", "\n"]}], "source": ["print_json(app_infos[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"metadata": {}}, "outputs": [], "source": ["\n", "df.to_csv('BankDigital/dataset/info_BANK_MOBILE_GOOGLE_PLAY_Update21092024.csv', index=None, header=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"metadata": {}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 7/7 [05:47<00:00, 49.70s/it]\n"]}], "source": ["app_reviews = []\n", "\n", "for ap in tqdm(app_packages):\n", "  for score in list(range(1, 6)):\n", "    for sort_order in [Sort.MOST_RELEVANT, Sort.NEWEST]:\n", "      rvs, _ = reviews(\n", "        ap,\n", "        lang='id',\n", "        country='id',\n", "        sort=sort_order,\n", "        count= 3000 if score == 3 else 1500,\n", "        filter_score_with=score\n", "      )\n", "      for r in rvs:\n", "        r['sortOrder'] = 'most_relevant' if sort_order == Sort.MOST_RELEVANT else 'newest'\n", "        r['appId'] = ap\n", "      app_reviews.extend(rvs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"metadata": {}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"appId\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"com.alloapp.yump\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"appVersion\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"1.42.44\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"at\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"2024-07-30 20:46:15\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"content\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"Sebetulnya bagus untuk mempermudah dan kelancaran dalam transaksi. <PERSON>pi sayang suka eror. <PERSON>ak akun saya sampe seharian gak bisa di buka untuk transaksi. <PERSON><PERSON>al sudah lapor ke costomerservise juga belum akan perkembangan kelanjutannya sampe malam ini\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"repliedAt\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"2024-07-31 11:52:45\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"replyContent\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"<PERSON>, mohon maaf untuk ketidak<PERSON>manan yang kamu alami. <PERSON><PERSON> kamu masih mengalami kendala bisa tanyakan langsung ke Allo Care di 080 4110 4110, atau melalui Live Chat kami di bit.ly/AlloCare. <PERSON><PERSON> ka<PERSON>, semoga harimu menyenangkan. -TA\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"reviewCreatedVersion\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"1.42.44\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"reviewId\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"9e6055d6-1efa-4bff-974c-0a4e8b015379\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"score\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[34m1\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"sortOrder\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"most_relevant\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"thumbsUpCount\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[34m73\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"userImage\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"https://play-lh.googleusercontent.com/a/ACg8ocJpF6rlQUu6BT5FWxQ_Fp_Q-FAxCWQO78wAS8juoVL_VWpWPA=mo\"\u001b[39;49;00m,\u001b[37m\u001b[39;49;00m\n", "\u001b[37m  \u001b[39;49;00m\u001b[94m\"userName\"\u001b[39;49;00m:\u001b[37m \u001b[39;49;00m\u001b[33m\"Suhardiyanto Ttv\"\u001b[39;49;00m\u001b[37m\u001b[39;49;00m\n", "}\u001b[37m\u001b[39;49;00m\n", "\n"]}], "source": ["print_json(app_reviews[1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"metadata": {}}, "outputs": [], "source": ["app_reviews_df = pd.DataFrame(app_reviews)\n", "app_reviews_df.to_csv('BankDigital/dataset/Newest_REVIEW_BANK_MOBILE_GOOGLE_PLAY_Update21092024.csv', index=None, header=True)\n", "app_reviews_df.to_excel('BankDigital/dataset/Newest_REVIEW_BANK_MOBILE_GOOGLE_PLAY_Update21092024.xlsx', index=None, header=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 99920 entries, 0 to 99919\n", "Data columns (total 13 columns):\n", " #   Column                Non-Null Count  Dtype         \n", "---  ------                --------------  -----         \n", " 0   reviewId              99920 non-null  object        \n", " 1   userName              99920 non-null  object        \n", " 2   userImage             99920 non-null  object        \n", " 3   content               99920 non-null  object        \n", " 4   score                 99920 non-null  int64         \n", " 5   thumbsUpCount         99920 non-null  int64         \n", " 6   reviewCreatedVersion  78367 non-null  object        \n", " 7   at                    99920 non-null  datetime64[ns]\n", " 8   replyContent          77397 non-null  object        \n", " 9   repliedAt             77397 non-null  datetime64[ns]\n", " 10  appVersion            78367 non-null  object        \n", " 11  sortOrder             99920 non-null  object        \n", " 12  appId                 99920 non-null  object        \n", "dtypes: datetime64[ns](2), int64(2), object(9)\n", "memory usage: 9.9+ MB\n"]}], "source": ["app_reviews_df.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["reviewId                    0\n", "userName                    0\n", "userImage                   0\n", "content                     0\n", "score                       0\n", "thumbsUpCount               0\n", "reviewCreatedVersion    21553\n", "at                          0\n", "replyContent            22523\n", "repliedAt               22523\n", "appVersion              21553\n", "sortOrder                   0\n", "appId                       0\n", "dtype: int64\n"]}], "source": ["# Check for null values\n", "print(app_reviews_df.isnull().sum())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_df = app_reviews_df[['appId', 'content', 'score', 'at']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>appId</th>\n", "      <th>content</th>\n", "      <th>score</th>\n", "      <th>at</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>com.alloapp.yump</td>\n", "      <td>Sangat mengecewakan . Cs sangat lambat Untuk k...</td>\n", "      <td>1</td>\n", "      <td>2024-07-22 02:44:38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>com.alloapp.yump</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> bagus untuk mempermudah dan kelanca...</td>\n", "      <td>1</td>\n", "      <td>2024-07-30 20:46:15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>com.alloapp.yump</td>\n", "      <td>Aplikasi ribet. Terlalu Banyak iming iming yg ...</td>\n", "      <td>1</td>\n", "      <td>2024-09-09 18:47:56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>com.alloapp.yump</td>\n", "      <td><PERSON><PERSON><PERSON> paylater, set<PERSON><PERSON> di gunakan...</td>\n", "      <td>1</td>\n", "      <td>2024-09-15 02:29:28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>com.alloapp.yump</td>\n", "      <td>ulasan terbaru yah, saya coba mengganti alamat...</td>\n", "      <td>1</td>\n", "      <td>2024-09-06 20:06:21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99915</th>\n", "      <td>com.bnc.finance</td>\n", "      <td>Apa2 bisa pake Neo bank pokonya jangan ragu pa...</td>\n", "      <td>5</td>\n", "      <td>2024-02-22 16:44:34</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99916</th>\n", "      <td>com.bnc.finance</td>\n", "      <td><PERSON><PERSON><PERSON> ini sih fitur deposito neo wow sih yang...</td>\n", "      <td>5</td>\n", "      <td>2024-02-22 16:42:48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99917</th>\n", "      <td>com.bnc.finance</td>\n", "      <td>Fitur neobank yang bikin happy banget adalah q...</td>\n", "      <td>5</td>\n", "      <td>2024-02-22 16:41:22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99918</th>\n", "      <td>com.bnc.finance</td>\n", "      <td>Mantap banget untuk deposit dan nabung jangka ...</td>\n", "      <td>5</td>\n", "      <td>2024-02-22 16:40:03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99919</th>\n", "      <td>com.bnc.finance</td>\n", "      <td>Aku sudah lama pakai NEO BANK .. dari deposto ...</td>\n", "      <td>5</td>\n", "      <td>2024-02-22 16:39:35</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>99920 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                  appId                                            content  \\\n", "0      com.alloapp.yump  Sangat mengecewakan . Cs sangat lambat Untuk k...   \n", "1      com.alloapp.yump  Sebetulnya bagus untuk mempermudah dan kelanca...   \n", "2      com.alloapp.yump  Aplikasi ribet. Terlalu Banyak iming iming yg ...   \n", "3      com.alloapp.yump  <PERSON><PERSON><PERSON> paylater, set<PERSON><PERSON> di gunakan...   \n", "4      com.alloapp.yump  ulasan terbaru yah, saya coba mengganti alamat...   \n", "...                 ...                                                ...   \n", "99915   com.bnc.finance  Apa2 bisa pake Neo bank pokonya jangan ragu pa...   \n", "99916   com.bnc.finance  Sejauh ini sih fitur deposito neo wow sih yang...   \n", "99917   com.bnc.finance  Fitur neobank yang bikin happy banget adalah q...   \n", "99918   com.bnc.finance  Mantap banget untuk deposit dan nabung jangka ...   \n", "99919   com.bnc.finance  Aku sudah lama pakai NEO BANK .. dari deposto ...   \n", "\n", "       score                  at  \n", "0          1 2024-07-22 02:44:38  \n", "1          1 2024-07-30 20:46:15  \n", "2          1 2024-09-09 18:47:56  \n", "3          1 2024-09-15 02:29:28  \n", "4          1 2024-09-06 20:06:21  \n", "...      ...                 ...  \n", "99915      5 2024-02-22 16:44:34  \n", "99916      5 2024-02-22 16:42:48  \n", "99917      5 2024-02-22 16:41:22  \n", "99918      5 2024-02-22 16:40:03  \n", "99919      5 2024-02-22 16:39:35  \n", "\n", "[99920 rows x 4 columns]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["data_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["appId      0\n", "content    0\n", "score      0\n", "at         0\n", "dtype: int64\n"]}], "source": ["print(data_df.isnull().sum())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_df.to_csv('BankDigital/dataset/REVIEW_BANK_MOBILE_Playstore_21092024.csv', index=None, header=True)\n", "data_df.to_excel('BankDigital/dataset/REVIEW_BANK_MOBILE_Playstore_21092024.xlsx', index=None, header=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}