{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Text Preprocessing Bahasa Indonesia\n", "\n", "This notebook performs comprehensive text preprocessing for Bahasa Indonesia including:\n", "1. Tokenization\n", "2. Text Filtering\n", "3. Text Normalization\n", "4. <PERSON><PERSON> Removal\n", "5. <PERSON><PERSON><PERSON> using Sa<PERSON>wi\n", "\n", "Each preprocessing step result will be saved to separate CSV columns."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: sastrawi in c:\\python312\\lib\\site-packages (1.0.1)\n", "Requirement already satisfied: pandas in c:\\python312\\lib\\site-packages (2.1.3)\n", "Requirement already satisfied: nltk in c:\\python312\\lib\\site-packages (3.8.1)\n", "Requirement already satisfied: regex in c:\\python312\\lib\\site-packages (2024.5.15)\n", "Requirement already satisfied: numpy<2,>=1.26.0 in c:\\python312\\lib\\site-packages (from pandas) (1.26.4)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from pandas) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\python312\\lib\\site-packages (from pandas) (2024.1)\n", "Requirement already satisfied: tzdata>=2022.1 in c:\\python312\\lib\\site-packages (from pandas) (2024.1)\n", "Requirement already satisfied: click in c:\\python312\\lib\\site-packages (from nltk) (8.1.7)\n", "Requirement already satisfied: joblib in c:\\python312\\lib\\site-packages (from nltk) (1.4.2)\n", "Requirement already satisfied: tqdm in c:\\python312\\lib\\site-packages (from nltk) (4.66.4)\n", "Requirement already satisfied: six>=1.5 in c:\\python312\\lib\\site-packages (from python-dateutil>=2.8.2->pandas) (1.16.0)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from click->nltk) (0.4.6)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: Ignoring invalid distribution ~ (C:\\Python312\\Lib\\site-packages)\n", "WARNING: Ignoring invalid distribution ~-p (C:\\Python312\\Lib\\site-packages)\n", "WARNING: Ignoring invalid distribution ~=p (C:\\Python312\\Lib\\site-packages)\n", "WARNING: Ignoring invalid distribution ~ip (C:\\Python312\\Lib\\site-packages)\n", "WARNING: Ignoring invalid distribution ~~p (C:\\Python312\\Lib\\site-packages)\n", "WARNING: Ignoring invalid distribution ~ (C:\\Python312\\Lib\\site-packages)\n", "WARNING: Ignoring invalid distribution ~-p (C:\\Python312\\Lib\\site-packages)\n", "WARNING: Ignoring invalid distribution ~=p (C:\\Python312\\Lib\\site-packages)\n", "WARNING: Ignoring invalid distribution ~ip (C:\\Python312\\Lib\\site-packages)\n", "WARNING: Ignoring invalid distribution ~~p (C:\\Python312\\Lib\\site-packages)\n", "WARNING: Ignoring invalid distribution ~ (C:\\Python312\\Lib\\site-packages)\n", "WARNING: Ignoring invalid distribution ~-p (C:\\Python312\\Lib\\site-packages)\n", "WARNING: Ignoring invalid distribution ~=p (C:\\Python312\\Lib\\site-packages)\n", "WARNING: Ignoring invalid distribution ~ip (C:\\Python312\\Lib\\site-packages)\n", "WARNING: Ignoring invalid distribution ~~p (C:\\Python312\\Lib\\site-packages)\n", "\n", "[notice] A new release of pip is available: 25.1 -> 25.1.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["# Install required packages\n", "!pip install sastrawi pandas nltk regex"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import re\n", "import string\n", "import nltk\n", "from nltk.tokenize import word_tokenize\n", "from Sastrawi.Stemmer.StemmerFactory import StemmerFactory\n", "from Sastrawi.StopWordRemover.StopWordRemoverFactory import StopWordRemoverFactory\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Download NLTK data\n", "nltk.download('punkt', quiet=True)\n", "nltk.download('punkt_tab', quiet=True)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset shape: (10000, 15)\n", "Columns: ['reviewId', 'userName', 'userImage', 'content', 'score', 'thumbsUpCount', 'reviewCreatedVersion', 'at', 'replyContent', 'repliedAt', 'appVersion', 'app_id', 'scraped_at', 'review_length', 'word_count']\n", "\n", "First 5 rows of content column:\n", "0    sangat mudah digunakan untuk melakukan sesuatu...\n", "1                                     mantap jiwa❤️👍👍👍\n", "2                                         membantu bgt\n", "3                 gangguan terus transfer sy g masuk ²\n", "4                        loading isi pulsa lama sekali\n", "Name: content, dtype: object\n"]}], "source": ["# Load the dataset\n", "df = pd.read_csv('google_play_reviews_DigitalBank_20250703_205650.csv')\n", "print(f\"Dataset shape: {df.shape}\")\n", "print(f\"Columns: {df.columns.tolist()}\")\n", "print(\"\\nFirst 5 rows of content column:\")\n", "print(df['content'].head())"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of stopwords: 126\n", "Sample stopwords: ['yang', 'untuk', 'pada', 'ke', 'para', 'namun', 'menurut', 'antara', 'dia', 'dua']\n"]}], "source": ["# Initialize Sastrawi components\n", "stemmer_factory = StemmerFactory()\n", "stemmer = stemmer_factory.create_stemmer()\n", "\n", "stopword_factory = StopWordRemoverFactory()\n", "stopword_remover = stopword_factory.create_stop_word_remover()\n", "stopwords = stopword_factory.get_stop_words()\n", "\n", "print(f\"Number of stopwords: {len(stopwords)}\")\n", "print(f\"Sample stopwords: {list(stopwords)[:10]}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Define preprocessing functions\n", "\n", "def tokenize_text(text):\n", "    \"\"\"Tokenize text into words\"\"\"\n", "    if pd.isna(text):\n", "        return []\n", "    try:\n", "        tokens = word_tokenize(str(text).lower())\n", "        return tokens\n", "    except:\n", "        return str(text).lower().split()\n", "\n", "def filter_text(tokens):\n", "    \"\"\"Filter tokens: remove punctuation, numbers, and very short words\"\"\"\n", "    if not tokens:\n", "        return []\n", "    \n", "    filtered_tokens = []\n", "    for token in tokens:\n", "        # Remove punctuation and numbers\n", "        if (token.isalpha() and \n", "            len(token) > 2 and \n", "            token not in string.punctuation):\n", "            filtered_tokens.append(token)\n", "    \n", "    return filtered_tokens\n", "\n", "def normalize_text(tokens):\n", "    \"\"\"Normalize text: handle common Indonesian text variations\"\"\"\n", "    if not tokens:\n", "        return []\n", "    \n", "    # Common Indonesian text normalizations\n", "    normalization_dict = {\n", "        'gak': 'tidak',\n", "        'ga': 'tidak',\n", "        'nggak': 'tidak',\n", "        'ngga': 'tidak',\n", "        'gk': 'tidak',\n", "        'tdk': 'tidak',\n", "        'bgt': 'banget',\n", "        'bgd': 'banget',\n", "        'bgt': 'banget',\n", "        'yg': 'yang',\n", "        'dgn': 'dengan',\n", "        'utk': 'untuk',\n", "        'krn': 'karena',\n", "        'krna': 'karena',\n", "        'tp': 'tapi',\n", "        'tpi': 'tapi',\n", "        'jd': 'jadi',\n", "        'jdi': 'jadi',\n", "        'sy': 'saya',\n", "        'aq': 'aku',\n", "        'ak': 'aku',\n", "        'klo': 'kalau',\n", "        'kalo': 'kalau',\n", "        'udh': 'sudah',\n", "        'udah': 'sudah',\n", "        'dah': 'sudah',\n", "        'blm': 'belum',\n", "        'blom': 'belum',\n", "        'hrs': 'harus',\n", "        'hrus': 'harus',\n", "        'bs': 'bisa',\n", "        'bsa': 'bisa',\n", "        'gmn': 'gimana',\n", "        'gmna': 'gimana',\n", "        'knp': 'kenapa',\n", "        'knpa': 'kenapa',\n", "        'emg': 'memang',\n", "        'emang': 'memang',\n", "        'org': 'orang',\n", "        'orng': 'orang'\n", "    }\n", "    \n", "    normalized_tokens = []\n", "    for token in tokens:\n", "        # Remove repeated characters (e.g., 'bagussss' -> 'bagus')\n", "        token = re.sub(r'(.)\\1{2,}', r'\\1', token)\n", "        \n", "        # Apply normalization dictionary\n", "        if token in normalization_dict:\n", "            token = normalization_dict[token]\n", "        \n", "        normalized_tokens.append(token)\n", "    \n", "    return normalized_tokens\n", "\n", "def remove_stopwords(tokens):\n", "    \"\"\"Remove Indonesian stopwords\"\"\"\n", "    if not tokens:\n", "        return []\n", "    \n", "    return [token for token in tokens if token not in stopwords]\n", "\n", "def stem_tokens(tokens):\n", "    \"\"\"Apply stemming using Sastrawi\"\"\"\n", "    if not tokens:\n", "        return []\n", "    \n", "    stemmed_tokens = []\n", "    for token in tokens:\n", "        stemmed = stemmer.stem(token)\n", "        stemmed_tokens.append(stemmed)\n", "    \n", "    return stemmed_tokens"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting text preprocessing...\n", "1. Tokenizing text...\n", "2. Filtering text...\n", "3. Normalizing text...\n", "4. Removing stopwords...\n", "5. Applying stemming...\n", "Text preprocessing completed!\n"]}], "source": ["# Apply preprocessing steps\n", "print(\"Starting text preprocessing...\")\n", "\n", "# Step 1: Tokenization\n", "print(\"1. Tokenizing text...\")\n", "df['tokens'] = df['content'].apply(tokenize_text)\n", "df['tokenized_text'] = df['tokens'].apply(lambda x: ' '.join(x) if x else '')\n", "\n", "# Step 2: Text Filtering\n", "print(\"2. Filtering text...\")\n", "df['filtered_tokens'] = df['tokens'].apply(filter_text)\n", "df['filtered_text'] = df['filtered_tokens'].apply(lambda x: ' '.join(x) if x else '')\n", "\n", "# Step 3: Text Normalization\n", "print(\"3. Normalizing text...\")\n", "df['normalized_tokens'] = df['filtered_tokens'].apply(normalize_text)\n", "df['normalized_text'] = df['normalized_tokens'].apply(lambda x: ' '.join(x) if x else '')\n", "\n", "# Step 4: Stopword Removal\n", "print(\"4. Removing stopwords...\")\n", "df['no_stopwords_tokens'] = df['normalized_tokens'].apply(remove_stopwords)\n", "df['no_stopwords_text'] = df['no_stopwords_tokens'].apply(lambda x: ' '.join(x) if x else '')\n", "\n", "# Step 5: <PERSON><PERSON><PERSON>\n", "print(\"5. Applying stemming...\")\n", "df['stemmed_tokens'] = df['no_stopwords_tokens'].apply(stem_tokens)\n", "df['stemmed_text'] = df['stemmed_tokens'].apply(lambda x: ' '.join(x) if x else '')\n", "\n", "print(\"Text preprocessing completed!\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sample preprocessing results:\n", "================================================================================\n", "\n", "Sample 1:\n", "Original: sangat mudah digunakan untuk melakukan sesuatu tanpa harus menunggu lama\n", "Tokenized: sangat mudah digunakan untuk melakukan sesuatu tanpa harus menunggu lama\n", "Filtered: sangat mudah digunakan untuk melakukan sesuatu tanpa harus menunggu lama\n", "Normalized: sangat mudah digunakan untuk melakukan sesuatu tanpa harus menunggu lama\n", "No Stopwords: sangat mudah digunakan melakukan menunggu lama\n", "Stemmed: sangat mudah guna laku tunggu lama\n", "--------------------------------------------------------------------------------\n", "\n", "Sample 2:\n", "Original: mantap jiwa❤️👍👍👍\n", "Tokenized: mantap jiwa❤️👍👍👍\n", "Filtered: mantap\n", "Normalized: mantap\n", "No Stopwords: mantap\n", "Stemmed: mantap\n", "--------------------------------------------------------------------------------\n", "\n", "Sample 3:\n", "Original: membantu bgt\n", "Tokenized: membantu bgt\n", "Filtered: membantu bgt\n", "Normalized: membantu banget\n", "No Stopwords: membantu banget\n", "Stemmed: bantu banget\n", "--------------------------------------------------------------------------------\n"]}], "source": ["# Display preprocessing results for sample texts\n", "print(\"Sample preprocessing results:\")\n", "print(\"=\" * 80)\n", "\n", "for i in range(min(3, len(df))):\n", "    print(f\"\\nSample {i+1}:\")\n", "    print(f\"Original: {df.iloc[i]['content']}\")\n", "    print(f\"Tokenized: {df.iloc[i]['tokenized_text']}\")\n", "    print(f\"Filtered: {df.iloc[i]['filtered_text']}\")\n", "    print(f\"Normalized: {df.iloc[i]['normalized_text']}\")\n", "    print(f\"No Stopwords: {df.iloc[i]['no_stopwords_text']}\")\n", "    print(f\"Stemmed: {df.iloc[i]['stemmed_text']}\")\n", "    print(\"-\" * 80)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Final dataset shape: (10000, 20)\n", "New preprocessing columns added: ['tokenized_text', 'filtered_text', 'normalized_text', 'no_stopwords_text', 'stemmed_text']\n"]}], "source": ["# Create final dataset with preprocessing results\n", "# Remove intermediate token columns to keep only text columns\n", "final_columns = [\n", "    'reviewId', 'userName', 'userImage', 'content', 'score', 'thumbsUpCount',\n", "    'reviewCreatedVersion', 'at', 'replyContent', 'repliedAt', 'appVersion',\n", "    'app_id', 'scraped_at', 'review_length', 'word_count',\n", "    'tokenized_text', 'filtered_text', 'normalized_text', \n", "    'no_stopwords_text', 'stemmed_text'\n", "]\n", "\n", "df_final = df[final_columns].copy()\n", "\n", "print(f\"Final dataset shape: {df_final.shape}\")\n", "print(f\"New preprocessing columns added: {['tokenized_text', 'filtered_text', 'normalized_text', 'no_stopwords_text', 'stemmed_text']}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Preprocessed data saved to: google_play_reviews_DigitalBank_preprocessed.csv\n", "Total rows: 10000\n", "Total columns: 20\n"]}], "source": ["# Save preprocessed data to new CSV file\n", "output_filename = 'google_play_reviews_DigitalBank_preprocessed.csv'\n", "df_final.to_csv(output_filename, index=False)\n", "\n", "print(f\"Preprocessed data saved to: {output_filename}\")\n", "print(f\"Total rows: {len(df_final)}\")\n", "print(f\"Total columns: {len(df_final.columns)}\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Preprocessing Statistics:\n", "==================================================\n", "Original: 12.95 average words per review\n", "Tokenized: 14.37 average words per review\n", "Filtered: 11.58 average words per review\n", "Normalized: 11.58 average words per review\n", "No Stopwords: 9.00 average words per review\n", "Stemmed: 8.99 average words per review\n", "\n", "Reduction from original:\n", "Tokenized: -11.0% reduction\n", "Filtered: 10.5% reduction\n", "Normalized: 10.5% reduction\n", "No Stopwords: 30.5% reduction\n", "Stemmed: 30.6% reduction\n"]}], "source": ["# Display statistics about preprocessing results\n", "print(\"\\nPreprocessing Statistics:\")\n", "print(\"=\" * 50)\n", "\n", "# Calculate average word counts for each preprocessing step\n", "stats = {\n", "    'Original': df_final['content'].apply(lambda x: len(str(x).split()) if pd.notna(x) else 0).mean(),\n", "    'Tokenized': df_final['tokenized_text'].apply(lambda x: len(x.split()) if x else 0).mean(),\n", "    'Filtered': df_final['filtered_text'].apply(lambda x: len(x.split()) if x else 0).mean(),\n", "    'Normalized': df_final['normalized_text'].apply(lambda x: len(x.split()) if x else 0).mean(),\n", "    'No Stopwords': df_final['no_stopwords_text'].apply(lambda x: len(x.split()) if x else 0).mean(),\n", "    'Stemmed': df_final['stemmed_text'].apply(lambda x: len(x.split()) if x else 0).mean()\n", "}\n", "\n", "for step, avg_words in stats.items():\n", "    print(f\"{step}: {avg_words:.2f} average words per review\")\n", "\n", "# Show reduction percentages\n", "original_avg = stats['Original']\n", "print(\"\\nReduction from original:\")\n", "for step, avg_words in stats.items():\n", "    if step != 'Original':\n", "        reduction = ((original_avg - avg_words) / original_avg) * 100\n", "        print(f\"{step}: {reduction:.1f}% reduction\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Final Dataset Information:\n", "==================================================\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 10000 entries, 0 to 9999\n", "Data columns (total 20 columns):\n", " #   Column                Non-Null Count  Dtype \n", "---  ------                --------------  ----- \n", " 0   reviewId              10000 non-null  object\n", " 1   userName              10000 non-null  object\n", " 2   userImage             10000 non-null  object\n", " 3   content               10000 non-null  object\n", " 4   score                 10000 non-null  int64 \n", " 5   thumbsUpCount         10000 non-null  int64 \n", " 6   reviewCreatedVersion  7652 non-null   object\n", " 7   at                    10000 non-null  object\n", " 8   replyContent          5691 non-null   object\n", " 9   repliedAt             5691 non-null   object\n", " 10  appVersion            7652 non-null   object\n", " 11  app_id                10000 non-null  object\n", " 12  scraped_at            10000 non-null  object\n", " 13  review_length         10000 non-null  int64 \n", " 14  word_count            10000 non-null  int64 \n", " 15  tokenized_text        10000 non-null  object\n", " 16  filtered_text         10000 non-null  object\n", " 17  normalized_text       10000 non-null  object\n", " 18  no_stopwords_text     10000 non-null  object\n", " 19  stemmed_text          10000 non-null  object\n", "dtypes: int64(4), object(16)\n", "memory usage: 1.5+ MB\n", "None\n", "\n", "First 3 rows of preprocessing columns:\n", "                                                                    content                                                            tokenized_text                                                             filtered_text                                                           normalized_text                               no_stopwords_text                        stemmed_text\n", "0  sangat mudah digunakan untuk melakukan sesuatu tanpa harus menunggu lama  sangat mudah digunakan untuk melakukan sesuatu tanpa harus menunggu lama  sangat mudah digunakan untuk melakukan sesuatu tanpa harus menunggu lama  sangat mudah digunakan untuk melakukan sesuatu tanpa harus menunggu lama  sangat mudah digunakan melakukan menunggu lama  sangat mudah guna laku tunggu lama\n", "1                                                          mantap jiwa❤️👍👍👍                                                          mantap jiwa❤️👍👍👍                                                                    mantap                                                                    mantap                                          mantap                              mantap\n", "2                                                              membantu bgt                                                              membantu bgt                                                              membantu bgt                                                           membantu banget                                 membantu banget                        bantu banget\n"]}], "source": ["# Display final dataset info\n", "print(\"\\nFinal Dataset Information:\")\n", "print(\"=\" * 50)\n", "print(df_final.info())\n", "\n", "print(\"\\nFirst 3 rows of preprocessing columns:\")\n", "preprocessing_cols = ['content', 'tokenized_text', 'filtered_text', 'normalized_text', 'no_stopwords_text', 'stemmed_text']\n", "print(df_final[preprocessing_cols].head(3).to_string())"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>reviewId</th>\n", "      <th>userName</th>\n", "      <th>userImage</th>\n", "      <th>content</th>\n", "      <th>score</th>\n", "      <th>thumbsUpCount</th>\n", "      <th>reviewCreatedVersion</th>\n", "      <th>at</th>\n", "      <th>replyContent</th>\n", "      <th>repliedAt</th>\n", "      <th>appVersion</th>\n", "      <th>app_id</th>\n", "      <th>scraped_at</th>\n", "      <th>review_length</th>\n", "      <th>word_count</th>\n", "      <th>tokenized_text</th>\n", "      <th>filtered_text</th>\n", "      <th>normalized_text</th>\n", "      <th>no_stopwords_text</th>\n", "      <th>stemmed_text</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1be1a93d-9f51-4611-83c9-04b82b0ddec8</td>\n", "      <td><PERSON><PERSON> yanti05</td>\n", "      <td>https://play-lh.googleusercontent.com/a-/ALV-U...</td>\n", "      <td>sangat mudah digunakan untuk melakukan sesuatu...</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>8.65.3</td>\n", "      <td>2025-07-02 20:35:08</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>8.65.3</td>\n", "      <td>com.jago.digitalBanking</td>\n", "      <td>2025-07-03T20:55:15.795381</td>\n", "      <td>72</td>\n", "      <td>10</td>\n", "      <td>sangat mudah digunakan untuk melakukan sesuatu...</td>\n", "      <td>sangat mudah digunakan untuk melakukan sesuatu...</td>\n", "      <td>sangat mudah digunakan untuk melakukan sesuatu...</td>\n", "      <td>sangat mudah digunakan melakukan menunggu lama</td>\n", "      <td>sangat mudah guna laku tunggu lama</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>b64ad41a-32c6-4541-b0f9-bf5569d966c7</td>\n", "      <td><PERSON><PERSON>87</td>\n", "      <td>https://play-lh.googleusercontent.com/a/ACg8oc...</td>\n", "      <td>mantap jiwa❤️👍👍👍</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>2025-07-02 20:26:19</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>com.jago.digitalBanking</td>\n", "      <td>2025-07-03T20:55:15.795381</td>\n", "      <td>16</td>\n", "      <td>2</td>\n", "      <td>mantap jiwa❤️👍👍👍</td>\n", "      <td>mantap</td>\n", "      <td>mantap</td>\n", "      <td>mantap</td>\n", "      <td>mantap</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>79870da4-736a-4231-a111-11c9fca52c05</td>\n", "      <td>Yusep</td>\n", "      <td>https://play-lh.googleusercontent.com/a/ACg8oc...</td>\n", "      <td>membantu bgt</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>2025-07-02 20:09:51</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>com.jago.digitalBanking</td>\n", "      <td>2025-07-03T20:55:15.795381</td>\n", "      <td>12</td>\n", "      <td>2</td>\n", "      <td>membantu bgt</td>\n", "      <td>membantu bgt</td>\n", "      <td>membantu banget</td>\n", "      <td>membantu banget</td>\n", "      <td>bantu banget</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>df833340-c8b6-456c-8050-ef291fec92ec</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>https://play-lh.googleusercontent.com/a/ACg8oc...</td>\n", "      <td>gangguan terus transfer sy g masuk ²</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>8.67.0</td>\n", "      <td>2025-07-02 19:57:12</td>\n", "      <td><PERSON><PERSON>, Jagoan. <PERSON>hon maaf atas kendala yang ter...</td>\n", "      <td>2025-07-02 21:42:48</td>\n", "      <td>8.67.0</td>\n", "      <td>com.jago.digitalBanking</td>\n", "      <td>2025-07-03T20:55:15.795381</td>\n", "      <td>36</td>\n", "      <td>7</td>\n", "      <td>gangguan terus transfer sy g masuk ²</td>\n", "      <td>gangguan terus transfer masuk</td>\n", "      <td>gangguan terus transfer masuk</td>\n", "      <td>gangguan terus transfer masuk</td>\n", "      <td>ganggu terus transfer masuk</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>56e7f5fb-ec7a-46d7-a7f2-4c6a837e49ae</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>https://play-lh.googleusercontent.com/a-/ALV-U...</td>\n", "      <td>loading isi pulsa lama sekali</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>8.67.0</td>\n", "      <td>2025-07-02 19:19:56</td>\n", "      <td><PERSON><PERSON>, Jagoan. <PERSON>hon maaf atas kendala yang ter...</td>\n", "      <td>2025-07-02 22:35:17</td>\n", "      <td>8.67.0</td>\n", "      <td>com.jago.digitalBanking</td>\n", "      <td>2025-07-03T20:55:15.795381</td>\n", "      <td>29</td>\n", "      <td>5</td>\n", "      <td>loading isi pulsa lama sekali</td>\n", "      <td>loading isi pulsa lama sekali</td>\n", "      <td>loading isi pulsa lama sekali</td>\n", "      <td>loading isi pulsa lama sekali</td>\n", "      <td>loading isi pulsa lama sekali</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9995</th>\n", "      <td>880d6bf7-f70a-4bbb-949e-9d088ffbcd5a</td>\n", "      <td>Sastra Sakti</td>\n", "      <td>https://play-lh.googleusercontent.com/a/ACg8oc...</td>\n", "      <td>Aplikasi ga jelas mau daftar tapi verivikasi w...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>2025-02-28 01:19:06</td>\n", "      <td><PERSON>, mohon maaf atas kendala yang terjadi....</td>\n", "      <td>2025-02-28 08:13:57</td>\n", "      <td>NaN</td>\n", "      <td>com.bnc.finance</td>\n", "      <td>2025-07-03T20:56:46.885400</td>\n", "      <td>63</td>\n", "      <td>10</td>\n", "      <td>aplik<PERSON> ga jelas mau daftar tapi verivikasi w...</td>\n", "      <td>ap<PERSON><PERSON> jelas mau daftar tapi verivikasi waja...</td>\n", "      <td>ap<PERSON><PERSON> jelas mau daftar tapi verivikasi waja...</td>\n", "      <td>ap<PERSON><PERSON> jelas mau daftar verivikasi wajah gag...</td>\n", "      <td>ap<PERSON><PERSON> jelas mau daftar verivikasi wajah gag...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9996</th>\n", "      <td>80270aa8-1500-470f-8428-6e8b0b7d1a31</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>https://play-lh.googleusercontent.com/a/ACg8oc...</td>\n", "      <td>DANA TIDAK KUNJUNG DIKEMBALIKAN TRANSFER VIA V...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>2025-02-28 01:06:17</td>\n", "      <td><PERSON>, mohon maaf atas kendala yang terjadi....</td>\n", "      <td>2025-02-28 08:13:21</td>\n", "      <td>NaN</td>\n", "      <td>com.bnc.finance</td>\n", "      <td>2025-07-03T20:56:46.885400</td>\n", "      <td>55</td>\n", "      <td>8</td>\n", "      <td>dana tidak kunjung dikembalikan transfer via v...</td>\n", "      <td>dana tidak kunjung dikembalikan transfer via g...</td>\n", "      <td>dana tidak kunjung dikembalikan transfer via g...</td>\n", "      <td>dana kunjung dikembalikan transfer via gabi<PERSON></td>\n", "      <td>dana kunjung kembali transfer via gabi<PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>9997</th>\n", "      <td>d5b0b35c-320b-467d-b2ec-7358ac588e2d</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>https://play-lh.googleusercontent.com/a/ACg8oc...</td>\n", "      <td>ok</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>3.4.40</td>\n", "      <td>2025-02-27 23:27:03</td>\n", "      <td><PERSON> ka<PERSON>, terima kasih sudah mencoba Neo Experi...</td>\n", "      <td>2025-02-28 08:02:55</td>\n", "      <td>3.4.40</td>\n", "      <td>com.bnc.finance</td>\n", "      <td>2025-07-03T20:56:46.885400</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>ok</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>9998</th>\n", "      <td>ddfdaf2c-8f05-4f77-b153-cd7b9e04a1d2</td>\n", "      <td>Bambang <PERSON></td>\n", "      <td>https://play-lh.googleusercontent.com/a-/ALV-U...</td>\n", "      <td>Sdh selesai kendalanya di nohp lama sy ganti k...</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>3.4.40</td>\n", "      <td>2025-02-27 22:38:26</td>\n", "      <td><PERSON>, apa<PERSON>a masih memiliki kendala maka u...</td>\n", "      <td>2025-04-01 09:54:23</td>\n", "      <td>3.4.40</td>\n", "      <td>com.bnc.finance</td>\n", "      <td>2025-07-03T20:56:46.885400</td>\n", "      <td>57</td>\n", "      <td>11</td>\n", "      <td>sdh selesai kendalanya di nohp lama sy ganti k...</td>\n", "      <td>sdh selesai kendalanya nohp lama ganti nohp baru</td>\n", "      <td>sdh selesai kendalanya nohp lama ganti nohp baru</td>\n", "      <td>sdh selesai kendalanya nohp lama ganti nohp baru</td>\n", "      <td>sdh selesai kendala nohp lama ganti nohp baru</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9999</th>\n", "      <td>050a9b68-db45-443d-8c9b-b877a335aa1b</td>\n", "      <td><PERSON>yn Prt</td>\n", "      <td>https://play-lh.googleusercontent.com/a-/ALV-U...</td>\n", "      <td><PERSON><PERSON>, saya memiliki limit pinjaman kenapa ...</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>3.4.40</td>\n", "      <td>2025-02-27 20:12:34</td>\n", "      <td><PERSON>, mohon maaf atas kendala yang terjadi....</td>\n", "      <td>2025-02-28 08:01:27</td>\n", "      <td>3.4.40</td>\n", "      <td>com.bnc.finance</td>\n", "      <td>2025-07-03T20:56:46.885400</td>\n", "      <td>137</td>\n", "      <td>21</td>\n", "      <td>halo neo , saya memiliki limit pinjaman kenapa...</td>\n", "      <td>halo neo saya memiliki limit pinjaman kenapa t...</td>\n", "      <td>halo neo saya memiliki limit pinjaman kenapa t...</td>\n", "      <td>halo neo memiliki limit pinjaman dipakai padah...</td>\n", "      <td>halo neo milik limit pinjam pakai padahal baya...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10000 rows × 20 columns</p>\n", "</div>"], "text/plain": ["                                  reviewId            userName  \\\n", "0     1be1a93d-9f51-4611-83c9-04b82b0ddec8        <PERSON><PERSON> yanti05   \n", "1     b64ad41a-32c6-4541-b0f9-bf5569d966c7        <PERSON><PERSON>87   \n", "2     79870da4-736a-4231-a111-11c9fca52c05               Yusep   \n", "3     df833340-c8b6-456c-8050-ef291fec92ec        <PERSON><PERSON>   \n", "4     56e7f5fb-ec7a-46d7-a7f2-4c6a837e49ae     <PERSON><PERSON><PERSON>   \n", "...                                    ...                 ...   \n", "9995  880d6bf7-f70a-4bbb-949e-9d088ffbcd5a        Sastra Sakti   \n", "9996  80270aa8-1500-470f-8428-6e8b0b7d1a31       <PERSON><PERSON>   \n", "9997  d5b0b35c-320b-467d-b2ec-7358ac588e2d         <PERSON><PERSON>   \n", "9998  ddfdaf2c-8f05-4f77-b153-cd7b9e04a1d2  Bambang Indradjaja   \n", "9999  050a9b68-db45-443d-8c9b-b877a335aa1b             Dyn Prt   \n", "\n", "                                              userImage  \\\n", "0     https://play-lh.googleusercontent.com/a-/ALV-U...   \n", "1     https://play-lh.googleusercontent.com/a/ACg8oc...   \n", "2     https://play-lh.googleusercontent.com/a/ACg8oc...   \n", "3     https://play-lh.googleusercontent.com/a/ACg8oc...   \n", "4     https://play-lh.googleusercontent.com/a-/ALV-U...   \n", "...                                                 ...   \n", "9995  https://play-lh.googleusercontent.com/a/ACg8oc...   \n", "9996  https://play-lh.googleusercontent.com/a/ACg8oc...   \n", "9997  https://play-lh.googleusercontent.com/a/ACg8oc...   \n", "9998  https://play-lh.googleusercontent.com/a-/ALV-U...   \n", "9999  https://play-lh.googleusercontent.com/a-/ALV-U...   \n", "\n", "                                                content  score  thumbsUpCount  \\\n", "0     sangat mudah digunakan untuk melakukan sesuatu...      5              0   \n", "1                                      mantap jiwa❤️👍👍👍      5              0   \n", "2                                          membantu bgt      5              0   \n", "3                  gangguan terus transfer sy g masuk ²      1              0   \n", "4                         loading isi pulsa lama sekali      3              0   \n", "...                                                 ...    ...            ...   \n", "9995  Aplikasi ga jelas mau daftar tapi verivikasi w...      1              0   \n", "9996  DANA TIDAK KUNJUNG DIKEMBALIKAN TRANSFER VIA V...      1              0   \n", "9997                                                 ok      5              0   \n", "9998  Sdh selesai kendalanya di nohp lama sy ganti k...      5              0   \n", "9999  <PERSON><PERSON>, saya memiliki limit pinjaman kenapa ...      4              0   \n", "\n", "     reviewCreatedVersion                   at  \\\n", "0                  8.65.3  2025-07-02 20:35:08   \n", "1                     NaN  2025-07-02 20:26:19   \n", "2                     NaN  2025-07-02 20:09:51   \n", "3                  8.67.0  2025-07-02 19:57:12   \n", "4                  8.67.0  2025-07-02 19:19:56   \n", "...                   ...                  ...   \n", "9995                  NaN  2025-02-28 01:19:06   \n", "9996                  NaN  2025-02-28 01:06:17   \n", "9997               3.4.40  2025-02-27 23:27:03   \n", "9998               3.4.40  2025-02-27 22:38:26   \n", "9999               3.4.40  2025-02-27 20:12:34   \n", "\n", "                                           replyContent            repliedAt  \\\n", "0                                                   NaN                  NaN   \n", "1                                                   NaN                  NaN   \n", "2                                                   NaN                  NaN   \n", "3     <PERSON><PERSON>, <PERSON><PERSON><PERSON>. <PERSON><PERSON> maaf atas kendala yang ter...  2025-07-02 21:42:48   \n", "4     <PERSON><PERSON>, <PERSON><PERSON><PERSON>. <PERSON><PERSON> maaf atas kendala yang ter...  2025-07-02 22:35:17   \n", "...                                                 ...                  ...   \n", "9995  <PERSON>, mohon maaf atas kendala yang terjadi....  2025-02-28 08:13:57   \n", "9996  <PERSON>, mohon maaf atas kendala yang terjadi....  2025-02-28 08:13:21   \n", "9997  <PERSON>, terima kasih sudah mencoba Neo Experi...  2025-02-28 08:02:55   \n", "9998  <PERSON>, a<PERSON><PERSON><PERSON> ma<PERSON><PERSON> memiliki kendala maka u...  2025-04-01 09:54:23   \n", "9999  <PERSON>, mohon maaf atas kendala yang terjadi....  2025-02-28 08:01:27   \n", "\n", "     appVersion                   app_id                  scraped_at  \\\n", "0        8.65.3  com.jago.digitalBanking  2025-07-03T20:55:15.795381   \n", "1           NaN  com.jago.digitalBanking  2025-07-03T20:55:15.795381   \n", "2           NaN  com.jago.digitalBanking  2025-07-03T20:55:15.795381   \n", "3        8.67.0  com.jago.digitalBanking  2025-07-03T20:55:15.795381   \n", "4        8.67.0  com.jago.digitalBanking  2025-07-03T20:55:15.795381   \n", "...         ...                      ...                         ...   \n", "9995        NaN          com.bnc.finance  2025-07-03T20:56:46.885400   \n", "9996        NaN          com.bnc.finance  2025-07-03T20:56:46.885400   \n", "9997     3.4.40          com.bnc.finance  2025-07-03T20:56:46.885400   \n", "9998     3.4.40          com.bnc.finance  2025-07-03T20:56:46.885400   \n", "9999     3.4.40          com.bnc.finance  2025-07-03T20:56:46.885400   \n", "\n", "      review_length  word_count  \\\n", "0                72          10   \n", "1                16           2   \n", "2                12           2   \n", "3                36           7   \n", "4                29           5   \n", "...             ...         ...   \n", "9995             63          10   \n", "9996             55           8   \n", "9997              2           1   \n", "9998             57          11   \n", "9999            137          21   \n", "\n", "                                         tokenized_text  \\\n", "0     sangat mudah digunakan untuk melakukan sesuatu...   \n", "1                                      mantap jiwa❤️👍👍👍   \n", "2                                          membantu bgt   \n", "3                  gangguan terus transfer sy g masuk ²   \n", "4                         loading isi pulsa lama sekali   \n", "...                                                 ...   \n", "9995  aplikasi ga jelas mau daftar tapi verivikasi w...   \n", "9996  dana tidak kunjung dikembalikan transfer via v...   \n", "9997                                                 ok   \n", "9998  sdh selesai kendalanya di nohp lama sy ganti k...   \n", "9999  halo neo , saya memiliki limit pinjaman kenapa...   \n", "\n", "                                          filtered_text  \\\n", "0     sangat mudah digunakan untuk melakukan sesuatu...   \n", "1                                                mantap   \n", "2                                          membantu bgt   \n", "3                         gangguan terus transfer masuk   \n", "4                         loading isi pulsa lama sekali   \n", "...                                                 ...   \n", "9995  aplikasi jelas mau daftar tapi verivikasi waja...   \n", "9996  dana tidak kunjung dikembalikan transfer via g...   \n", "9997                                                      \n", "9998   sdh selesai kendalanya nohp lama ganti nohp baru   \n", "9999  halo neo saya memiliki limit pinjaman kenapa t...   \n", "\n", "                                        normalized_text  \\\n", "0     sangat mudah digunakan untuk melakukan sesuatu...   \n", "1                                                mantap   \n", "2                                       membantu banget   \n", "3                         gangguan terus transfer masuk   \n", "4                         loading isi pulsa lama sekali   \n", "...                                                 ...   \n", "9995  aplikasi jelas mau daftar tapi verivikasi waja...   \n", "9996  dana tidak kunjung dikembalikan transfer via g...   \n", "9997                                                      \n", "9998   sdh selesai kendalanya nohp lama ganti nohp baru   \n", "9999  halo neo saya memiliki limit pinjaman kenapa t...   \n", "\n", "                                      no_stopwords_text  \\\n", "0        sangat mudah digunakan melakukan menunggu lama   \n", "1                                                mantap   \n", "2                                       membantu banget   \n", "3                         gangguan terus transfer masuk   \n", "4                         loading isi pulsa lama sekali   \n", "...                                                 ...   \n", "9995  aplikasi jelas mau daftar verivikasi wajah gag...   \n", "9996      dana kunjung dikembalikan transfer via gabisa   \n", "9997                                                      \n", "9998   sdh selesai kendalanya nohp lama ganti nohp baru   \n", "9999  halo neo memiliki limit pinjaman dipakai padah...   \n", "\n", "                                           stemmed_text  \n", "0                    sangat mudah guna laku tunggu lama  \n", "1                                                mantap  \n", "2                                          bantu banget  \n", "3                           ganggu terus transfer masuk  \n", "4                         loading isi pulsa lama sekali  \n", "...                                                 ...  \n", "9995  aplikasi jelas mau daftar verivikasi wajah gag...  \n", "9996           dana kunjung kembali transfer via gabisa  \n", "9997                                                     \n", "9998      sdh selesai kendala nohp lama ganti nohp baru  \n", "9999  halo neo milik limit pinjam pakai padahal baya...  \n", "\n", "[10000 rows x 20 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df_final"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}