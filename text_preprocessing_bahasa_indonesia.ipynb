# Install required packages
!pip install sastrawi pandas nltk regex

import pandas as pd
import re
import string
import nltk
from nltk.tokenize import word_tokenize
from Sastrawi.Stemmer.StemmerFactory import StemmerFactory
from Sastrawi.StopWordRemover.StopWordRemoverFactory import StopWordRemoverFactory
import warnings
warnings.filterwarnings('ignore')

# Download NLTK data
nltk.download('punkt', quiet=True)
nltk.download('punkt_tab', quiet=True)

# Load the dataset
df = pd.read_csv('google_play_reviews_DigitalBank_20250703_205650.csv')
print(f"Dataset shape: {df.shape}")
print(f"Columns: {df.columns.tolist()}")
print("\nFirst 5 rows of content column:")
print(df['content'].head())

# Initialize Sastrawi components
stemmer_factory = StemmerFactory()
stemmer = stemmer_factory.create_stemmer()

stopword_factory = StopWordRemoverFactory()
stopword_remover = stopword_factory.create_stop_word_remover()
stopwords = stopword_factory.get_stop_words()

print(f"Number of stopwords: {len(stopwords)}")
print(f"Sample stopwords: {list(stopwords)[:10]}")

# Define preprocessing functions

def tokenize_text(text):
    """Tokenize text into words"""
    if pd.isna(text):
        return []
    try:
        tokens = word_tokenize(str(text).lower())
        return tokens
    except:
        return str(text).lower().split()

def filter_text(tokens):
    """Filter tokens: remove punctuation, numbers, and very short words"""
    if not tokens:
        return []
    
    filtered_tokens = []
    for token in tokens:
        # Remove punctuation and numbers
        if (token.isalpha() and 
            len(token) > 2 and 
            token not in string.punctuation):
            filtered_tokens.append(token)
    
    return filtered_tokens

def normalize_text(tokens):
    """Normalize text: handle common Indonesian text variations"""
    if not tokens:
        return []
    
    # Common Indonesian text normalizations
    normalization_dict = {
        'gak': 'tidak',
        'ga': 'tidak',
        'nggak': 'tidak',
        'ngga': 'tidak',
        'gk': 'tidak',
        'tdk': 'tidak',
        'bgt': 'banget',
        'bgd': 'banget',
        'bgt': 'banget',
        'yg': 'yang',
        'dgn': 'dengan',
        'utk': 'untuk',
        'krn': 'karena',
        'krna': 'karena',
        'tp': 'tapi',
        'tpi': 'tapi',
        'jd': 'jadi',
        'jdi': 'jadi',
        'sy': 'saya',
        'aq': 'aku',
        'ak': 'aku',
        'klo': 'kalau',
        'kalo': 'kalau',
        'udh': 'sudah',
        'udah': 'sudah',
        'dah': 'sudah',
        'blm': 'belum',
        'blom': 'belum',
        'hrs': 'harus',
        'hrus': 'harus',
        'bs': 'bisa',
        'bsa': 'bisa',
        'gmn': 'gimana',
        'gmna': 'gimana',
        'knp': 'kenapa',
        'knpa': 'kenapa',
        'emg': 'memang',
        'emang': 'memang',
        'org': 'orang',
        'orng': 'orang'
    }
    
    normalized_tokens = []
    for token in tokens:
        # Remove repeated characters (e.g., 'bagussss' -> 'bagus')
        token = re.sub(r'(.)\1{2,}', r'\1', token)
        
        # Apply normalization dictionary
        if token in normalization_dict:
            token = normalization_dict[token]
        
        normalized_tokens.append(token)
    
    return normalized_tokens

def remove_stopwords(tokens):
    """Remove Indonesian stopwords"""
    if not tokens:
        return []
    
    return [token for token in tokens if token not in stopwords]

def stem_tokens(tokens):
    """Apply stemming using Sastrawi"""
    if not tokens:
        return []
    
    stemmed_tokens = []
    for token in tokens:
        stemmed = stemmer.stem(token)
        stemmed_tokens.append(stemmed)
    
    return stemmed_tokens

# Apply preprocessing steps
print("Starting text preprocessing...")

# Step 1: Tokenization
print("1. Tokenizing text...")
df['tokens'] = df['content'].apply(tokenize_text)
df['tokenized_text'] = df['tokens'].apply(lambda x: ' '.join(x) if x else '')

# Step 2: Text Filtering
print("2. Filtering text...")
df['filtered_tokens'] = df['tokens'].apply(filter_text)
df['filtered_text'] = df['filtered_tokens'].apply(lambda x: ' '.join(x) if x else '')

# Step 3: Text Normalization
print("3. Normalizing text...")
df['normalized_tokens'] = df['filtered_tokens'].apply(normalize_text)
df['normalized_text'] = df['normalized_tokens'].apply(lambda x: ' '.join(x) if x else '')

# Step 4: Stopword Removal
print("4. Removing stopwords...")
df['no_stopwords_tokens'] = df['normalized_tokens'].apply(remove_stopwords)
df['no_stopwords_text'] = df['no_stopwords_tokens'].apply(lambda x: ' '.join(x) if x else '')

# Step 5: Stemming
print("5. Applying stemming...")
df['stemmed_tokens'] = df['no_stopwords_tokens'].apply(stem_tokens)
df['stemmed_text'] = df['stemmed_tokens'].apply(lambda x: ' '.join(x) if x else '')

print("Text preprocessing completed!")

# Display preprocessing results for sample texts
print("Sample preprocessing results:")
print("=" * 80)

for i in range(min(3, len(df))):
    print(f"\nSample {i+1}:")
    print(f"Original: {df.iloc[i]['content']}")
    print(f"Tokenized: {df.iloc[i]['tokenized_text']}")
    print(f"Filtered: {df.iloc[i]['filtered_text']}")
    print(f"Normalized: {df.iloc[i]['normalized_text']}")
    print(f"No Stopwords: {df.iloc[i]['no_stopwords_text']}")
    print(f"Stemmed: {df.iloc[i]['stemmed_text']}")
    print("-" * 80)

# Create final dataset with preprocessing results
# Remove intermediate token columns to keep only text columns
final_columns = [
    'reviewId', 'userName', 'userImage', 'content', 'score', 'thumbsUpCount',
    'reviewCreatedVersion', 'at', 'replyContent', 'repliedAt', 'appVersion',
    'app_id', 'scraped_at', 'review_length', 'word_count',
    'tokenized_text', 'filtered_text', 'normalized_text', 
    'no_stopwords_text', 'stemmed_text'
]

df_final = df[final_columns].copy()

print(f"Final dataset shape: {df_final.shape}")
print(f"New preprocessing columns added: {['tokenized_text', 'filtered_text', 'normalized_text', 'no_stopwords_text', 'stemmed_text']}")

# Save preprocessed data to new CSV file
output_filename = 'google_play_reviews_DigitalBank_preprocessed.csv'
df_final.to_csv(output_filename, index=False)

print(f"Preprocessed data saved to: {output_filename}")
print(f"Total rows: {len(df_final)}")
print(f"Total columns: {len(df_final.columns)}")

# Display statistics about preprocessing results
print("\nPreprocessing Statistics:")
print("=" * 50)

# Calculate average word counts for each preprocessing step
stats = {
    'Original': df_final['content'].apply(lambda x: len(str(x).split()) if pd.notna(x) else 0).mean(),
    'Tokenized': df_final['tokenized_text'].apply(lambda x: len(x.split()) if x else 0).mean(),
    'Filtered': df_final['filtered_text'].apply(lambda x: len(x.split()) if x else 0).mean(),
    'Normalized': df_final['normalized_text'].apply(lambda x: len(x.split()) if x else 0).mean(),
    'No Stopwords': df_final['no_stopwords_text'].apply(lambda x: len(x.split()) if x else 0).mean(),
    'Stemmed': df_final['stemmed_text'].apply(lambda x: len(x.split()) if x else 0).mean()
}

for step, avg_words in stats.items():
    print(f"{step}: {avg_words:.2f} average words per review")

# Show reduction percentages
original_avg = stats['Original']
print("\nReduction from original:")
for step, avg_words in stats.items():
    if step != 'Original':
        reduction = ((original_avg - avg_words) / original_avg) * 100
        print(f"{step}: {reduction:.1f}% reduction")

# Display final dataset info
print("\nFinal Dataset Information:")
print("=" * 50)
print(df_final.info())

print("\nFirst 3 rows of preprocessing columns:")
preprocessing_cols = ['content', 'tokenized_text', 'filtered_text', 'normalized_text', 'no_stopwords_text', 'stemmed_text']
print(df_final[preprocessing_cols].head(3).to_string())

df_final