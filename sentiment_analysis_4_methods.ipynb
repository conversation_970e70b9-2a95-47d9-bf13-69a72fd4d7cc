!pip install vadersentiment

# Import required libraries
import pandas as pd
import numpy as np
from textblob import TextBlob
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# Set display options
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', 100)

# Load the preprocessed data
df = pd.read_csv('google_play_reviews_DigitalBank_preprocessed.csv')
print(f"Dataset shape: {df.shape}")
print(f"\nColumns: {list(df.columns)}")
print(f"\nFirst few rows of stemmed_text:")
print(df[['score', 'stemmed_text']].head(10))

# Check for missing values in key columns
print("Missing values:")
print(f"Score: {df['score'].isnull().sum()}")
print(f"Stemmed text: {df['stemmed_text'].isnull().sum()}")

# Remove rows with missing stemmed_text
df_clean = df.dropna(subset=['stemmed_text']).copy()
print(f"\nDataset shape after removing missing stemmed_text: {df_clean.shape}")

# Score distribution
print(f"\nScore distribution:")
print(df_clean['score'].value_counts().sort_index())

# Method 1: Score-based sentiment labeling
def score_based_sentiment(score):
    """
    Convert score to sentiment label:
    1-2: negative
    3: neutral
    4-5: positive
    """
    if pd.isna(score):
        return 'unknown'
    elif score in [1, 2]:
        return 'negative'
    elif score == 3:
        return 'neutral'
    elif score in [4, 5]:
        return 'positive'
    else:
        return 'unknown'

# Apply score-based sentiment
df_clean['sentiment_score_based'] = df_clean['score'].apply(score_based_sentiment)

print("Score-based sentiment distribution:")
print(df_clean['sentiment_score_based'].value_counts())

# Visualize score-based sentiment distribution
plt.figure(figsize=(8, 6))
df_clean['sentiment_score_based'].value_counts().plot(kind='bar', color=['red', 'gray', 'green', 'orange'])
plt.title('Score-based Sentiment Distribution')
plt.xlabel('Sentiment')
plt.ylabel('Count')
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

# Method 2: TextBlob sentiment analysis
def textblob_sentiment(text):
    """
    Analyze sentiment using TextBlob
    Returns polarity score and sentiment label
    """
    if pd.isna(text) or text.strip() == '':
        return 0.0, 'neutral'
    
    try:
        blob = TextBlob(str(text))
        polarity = blob.sentiment.polarity
        
        if polarity > 0.1:
            sentiment = 'positive'
        elif polarity < -0.1:
            sentiment = 'negative'
        else:
            sentiment = 'neutral'
            
        return polarity, sentiment
    except:
        return 0.0, 'neutral'

# Apply TextBlob sentiment analysis
print("Applying TextBlob sentiment analysis...")
textblob_results = df_clean['stemmed_text'].apply(textblob_sentiment)
df_clean['textblob_polarity'] = [result[0] for result in textblob_results]
df_clean['sentiment_textblob'] = [result[1] for result in textblob_results]

print("\nTextBlob sentiment distribution:")
print(df_clean['sentiment_textblob'].value_counts())

print("\nTextBlob polarity statistics:")
print(df_clean['textblob_polarity'].describe())

# Method 3: VADER sentiment analysis
analyzer = SentimentIntensityAnalyzer()

def vader_sentiment(text):
    """
    Analyze sentiment using VADER
    Returns compound score and sentiment label
    """
    if pd.isna(text) or text.strip() == '':
        return 0.0, 'neutral'
    
    try:
        scores = analyzer.polarity_scores(str(text))
        compound = scores['compound']
        
        if compound >= 0.05:
            sentiment = 'positive'
        elif compound <= -0.05:
            sentiment = 'negative'
        else:
            sentiment = 'neutral'
            
        return compound, sentiment
    except:
        return 0.0, 'neutral'

# Apply VADER sentiment analysis
print("Applying VADER sentiment analysis...")
vader_results = df_clean['stemmed_text'].apply(vader_sentiment)
df_clean['vader_compound'] = [result[0] for result in vader_results]
df_clean['sentiment_vader'] = [result[1] for result in vader_results]

print("\nVADER sentiment distribution:")
print(df_clean['sentiment_vader'].value_counts())

print("\nVADER compound score statistics:")
print(df_clean['vader_compound'].describe())

# Method 4: Ensemble weighted voting
def ensemble_sentiment(score_sentiment, textblob_sentiment, vader_sentiment):
    """
    Combine three sentiment methods using weighted voting
    Weights: Score-based=0.4, TextBlob=0.3, VADER=0.3
    """
    # Define weights
    weights = {
        'score_based': 0.4,
        'textblob': 0.3,
        'vader': 0.3
    }
    
    # Count votes for each sentiment
    sentiment_scores = {'positive': 0, 'negative': 0, 'neutral': 0}
    
    # Add weighted votes
    if score_sentiment in sentiment_scores:
        sentiment_scores[score_sentiment] += weights['score_based']
    
    if textblob_sentiment in sentiment_scores:
        sentiment_scores[textblob_sentiment] += weights['textblob']
    
    if vader_sentiment in sentiment_scores:
        sentiment_scores[vader_sentiment] += weights['vader']
    
    # Return sentiment with highest weighted score
    return max(sentiment_scores, key=sentiment_scores.get)

# Apply ensemble sentiment
print("Applying ensemble weighted voting...")
df_clean['sentiment_ensemble'] = df_clean.apply(
    lambda row: ensemble_sentiment(
        row['sentiment_score_based'],
        row['sentiment_textblob'],
        row['sentiment_vader']
    ), axis=1
)

print("\nEnsemble sentiment distribution:")
print(df_clean['sentiment_ensemble'].value_counts())

# Compare all sentiment methods
sentiment_columns = ['sentiment_score_based', 'sentiment_textblob', 'sentiment_vader', 'sentiment_ensemble']

print("Sentiment distribution comparison:")
comparison_df = pd.DataFrame()
for col in sentiment_columns:
    comparison_df[col] = df_clean[col].value_counts()

comparison_df = comparison_df.fillna(0).astype(int)
print(comparison_df)

# Visualize comparison
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

colors = ['red', 'gray', 'green']
method_names = ['Score-based', 'TextBlob', 'VADER', 'Ensemble']

for i, col in enumerate(sentiment_columns):
    sentiment_counts = df_clean[col].value_counts()
    axes[i].pie(sentiment_counts.values, labels=sentiment_counts.index, autopct='%1.1f%%', 
                colors=colors[:len(sentiment_counts)], startangle=90)
    axes[i].set_title(f'{method_names[i]} Sentiment Distribution')

plt.tight_layout()
plt.show()

# Agreement analysis between methods
print("Agreement analysis between sentiment methods:")
print("\n1. Score-based vs TextBlob:")
agreement_1 = (df_clean['sentiment_score_based'] == df_clean['sentiment_textblob']).mean()
print(f"Agreement: {agreement_1:.3f} ({agreement_1*100:.1f}%)")

print("\n2. Score-based vs VADER:")
agreement_2 = (df_clean['sentiment_score_based'] == df_clean['sentiment_vader']).mean()
print(f"Agreement: {agreement_2:.3f} ({agreement_2*100:.1f}%)")

print("\n3. TextBlob vs VADER:")
agreement_3 = (df_clean['sentiment_textblob'] == df_clean['sentiment_vader']).mean()
print(f"Agreement: {agreement_3:.3f} ({agreement_3*100:.1f}%)")

print("\n4. All three methods agree:")
all_agree = ((df_clean['sentiment_score_based'] == df_clean['sentiment_textblob']) & 
             (df_clean['sentiment_textblob'] == df_clean['sentiment_vader'])).mean()
print(f"Agreement: {all_agree:.3f} ({all_agree*100:.1f}%)")

# Confusion matrix style comparison
from sklearn.metrics import confusion_matrix, classification_report
import seaborn as sns

# Create confusion matrices
fig, axes = plt.subplots(1, 3, figsize=(18, 5))

# Score-based vs TextBlob
cm1 = confusion_matrix(df_clean['sentiment_score_based'], df_clean['sentiment_textblob'], 
                       labels=['negative', 'neutral', 'positive'])
sns.heatmap(cm1, annot=True, fmt='d', cmap='Blues', 
            xticklabels=['negative', 'neutral', 'positive'],
            yticklabels=['negative', 'neutral', 'positive'], ax=axes[0])
axes[0].set_title('Score-based vs TextBlob')
axes[0].set_xlabel('TextBlob')
axes[0].set_ylabel('Score-based')

# Score-based vs VADER
cm2 = confusion_matrix(df_clean['sentiment_score_based'], df_clean['sentiment_vader'], 
                       labels=['negative', 'neutral', 'positive'])
sns.heatmap(cm2, annot=True, fmt='d', cmap='Greens', 
            xticklabels=['negative', 'neutral', 'positive'],
            yticklabels=['negative', 'neutral', 'positive'], ax=axes[1])
axes[1].set_title('Score-based vs VADER')
axes[1].set_xlabel('VADER')
axes[1].set_ylabel('Score-based')

# TextBlob vs VADER
cm3 = confusion_matrix(df_clean['sentiment_textblob'], df_clean['sentiment_vader'], 
                       labels=['negative', 'neutral', 'positive'])
sns.heatmap(cm3, annot=True, fmt='d', cmap='Oranges', 
            xticklabels=['negative', 'neutral', 'positive'],
            yticklabels=['negative', 'neutral', 'positive'], ax=axes[2])
axes[2].set_title('TextBlob vs VADER')
axes[2].set_xlabel('VADER')
axes[2].set_ylabel('TextBlob')

plt.tight_layout()
plt.show()

# Sample analysis - show examples of each sentiment
print("Sample reviews for each sentiment (Ensemble method):")
print("\n" + "="*80)

for sentiment in ['positive', 'neutral', 'negative']:
    print(f"\n{sentiment.upper()} SENTIMENT EXAMPLES:")
    print("-" * 50)
    
    samples = df_clean[df_clean['sentiment_ensemble'] == sentiment].head(3)
    
    for idx, row in samples.iterrows():
        print(f"\nScore: {row['score']} | Original: {row['content'][:100]}...")
        print(f"Stemmed: {row['stemmed_text']}")
        print(f"Methods: Score={row['sentiment_score_based']}, TextBlob={row['sentiment_textblob']}, VADER={row['sentiment_vader']}, Ensemble={row['sentiment_ensemble']}")
        print(f"TextBlob polarity: {row['textblob_polarity']:.3f}, VADER compound: {row['vader_compound']:.3f}")
        print("-" * 50)

# Save results to new CSV file
output_columns = [
    'reviewId', 'content', 'score', 'stemmed_text',
    'sentiment_score_based', 'sentiment_textblob', 'sentiment_vader', 'sentiment_ensemble',
    'textblob_polarity', 'vader_compound'
]

# Select only the columns we want to save
df_output = df_clean[output_columns].copy()

# Save to new CSV file
output_filename = 'google_play_reviews_DigitalBank_sentiment_analysis.csv'
df_output.to_csv(output_filename, index=False)

print(f"Results saved to: {output_filename}")
print(f"Output dataset shape: {df_output.shape}")
print(f"\nColumns in output file: {list(df_output.columns)}")

# Final summary statistics
print("FINAL SENTIMENT ANALYSIS SUMMARY")
print("=" * 50)

print(f"\nTotal reviews analyzed: {len(df_clean):,}")

print("\nSentiment distribution by method:")
for method in ['sentiment_score_based', 'sentiment_textblob', 'sentiment_vader', 'sentiment_ensemble']:
    method_name = method.replace('sentiment_', '').replace('_', ' ').title()
    print(f"\n{method_name}:")
    counts = df_clean[method].value_counts()
    for sentiment, count in counts.items():
        percentage = (count / len(df_clean)) * 100
        print(f"  {sentiment}: {count:,} ({percentage:.1f}%)")

print("\nMethod agreement rates:")
print(f"Score-based vs TextBlob: {agreement_1:.1%}")
print(f"Score-based vs VADER: {agreement_2:.1%}")
print(f"TextBlob vs VADER: {agreement_3:.1%}")
print(f"All three methods: {all_agree:.1%}")

print(f"\nOutput file saved: {output_filename}")